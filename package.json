{"name": "rocket-bi-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@chromatic-com/storybook": "^4.1.1", "@storybook/addon-a11y": "^9.1.3", "@storybook/addon-docs": "^9.1.3", "@storybook/addon-vitest": "^9.1.3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "storybook": "^9.1.3", "tailwindcss": "^4", "typescript": "^5", "@storybook/nextjs-vite": "^9.1.3", "vitest": "^3.2.4", "@vitest/browser": "^3.2.4", "playwright": "^1.55.0", "@vitest/coverage-v8": "^3.2.4"}}