# Rocket BI v2

This is a [Next.js](https://nextjs.org) project with a complete testing and Storybook setup for UI development.

## Features

- ⚡ **Next.js 15** with App Router
- 🎨 **Tailwind CSS** for styling
- 🧪 **Jest + React Testing Library** for testing
- 📚 **Storybook** for component development
- 🔧 **TypeScript** for type safety
- 🎯 **Biome** for linting and formatting

## Getting Started

### Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Testing

Run tests:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

Run tests with coverage:
```bash
npm run test:coverage
```

### Storybook

Run Storybook for component development:
```bash
npm run storybook
```

Open [http://localhost:6006](http://localhost:6006) to view Storybook.

Build Storybook:
```bash
npm run build-storybook
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   └── Button/         # Example component with tests and stories
├── __tests__/          # Test utilities and setup
│   └── utils/
└── app/                # Next.js app directory
```

## Testing Setup

- **Jest** configured with Next.js integration
- **React Testing Library** for component testing
- **jsdom** environment for DOM testing
- Custom test utilities in `src/__tests__/utils/test-utils.tsx`
- Coverage reporting with thresholds (70% minimum)

## Storybook Setup

- **Storybook 9** with Next.js integration
- **Accessibility addon** for a11y testing
- **Docs addon** for automatic documentation
- **Actions addon** for interaction testing

## Example Component

The project includes a complete example `Button` component with:
- TypeScript interface
- Comprehensive tests
- Storybook stories
- Tailwind CSS styling

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run Biome linter
- `npm run format` - Format code with Biome
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run storybook` - Start Storybook
- `npm run build-storybook` - Build Storybook

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Storybook Documentation](https://storybook.js.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
