import { render, screen, fireEvent } from '@/__tests__/utils/test-utils'
import { Button } from './Button'

describe('Button', () => {
  it('renders with label', () => {
    render(<Button label="Test Button" />)
    expect(screen.getByRole('button', { name: 'Test Button' })).toBeInTheDocument()
  })

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn()
    render(<Button label="Click me" onClick={handleClick} />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies primary styles when primary prop is true', () => {
    render(<Button label="Primary Button" primary />)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-blue-600', 'text-white')
  })

  it('applies secondary styles when primary prop is false', () => {
    render(<Button label="Secondary Button" primary={false} />)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-transparent', 'text-gray-900', 'border-gray-300')
  })

  it('applies correct size classes', () => {
    const { rerender } = render(<Button label="Small" size="small" />)
    expect(screen.getByRole('button')).toHaveClass('px-3', 'py-1.5', 'text-sm')

    rerender(<Button label="Medium" size="medium" />)
    expect(screen.getByRole('button')).toHaveClass('px-4', 'py-2', 'text-base')

    rerender(<Button label="Large" size="large" />)
    expect(screen.getByRole('button')).toHaveClass('px-6', 'py-3', 'text-lg')
  })

  it('is disabled when disabled prop is true', () => {
    render(<Button label="Disabled Button" disabled />)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed')
  })

  it('applies custom background color', () => {
    render(<Button label="Custom Color" backgroundColor="red" />)
    const button = screen.getByRole('button')
    expect(button).toHaveStyle('background-color: rgb(255, 0, 0)')
  })

  it('does not call onClick when disabled', () => {
    const handleClick = jest.fn()
    render(<Button label="Disabled Button" onClick={handleClick} disabled />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).not.toHaveBeenCalled()
  })
})
